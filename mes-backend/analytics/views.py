import requests
import pandas as pd
import json
from rest_framework import viewsets, status, mixins, filters
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from .permissions import AnalyticsPermission
from rest_framework.response import Response
from authentication.decorators import requires_permission
from django.conf import settings
from django.db import transaction
from django.shortcuts import get_object_or_404

from .models import Dashboard, Chart, ChartGroup
from .serializers import DashboardSerializer, ChartSerializer, ChartGroupSerializer
from .schema_serializers import FilterSchemaSerializer
from .services import ClickHouseService, ChartService, FilterService
from .filter_schema_service import FilterSchemaService


class AnalyticsViewSet(viewsets.ViewSet):
    permission_classes = [IsAuthenticated]

   #@requires_permission(('analytics', 'get'))

    @action(detail=False, methods=['get'], url_path='embed_url')
    def embed_url(self, request):
        """
        Get the embedded URL for analytics dashboard
        """



        try:
            # Get configuration from environment variables
            base_url = getattr(settings, 'AWS_ANALYTICS_API_BASE_URL')
            dashboard_id = getattr(settings, 'AWS_ANALYTICS_DASHBOARD_ID')

            if not base_url or not dashboard_id:
                return Response(
                    {'error': 'Analytics configuration is missing'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

            # Construct the full URL
            external_api_url = f"{base_url}?dashboard_id={dashboard_id}"

            # Make the external API call
            response = requests.get(external_api_url, timeout=10)
            response.raise_for_status()  # Raises an HTTPError for bad responses

            return Response(response.json())

        except requests.Timeout:
            return Response(
                {'error': 'Analytics service timeout'},
                status=status.HTTP_504_GATEWAY_TIMEOUT
            )
        except requests.HTTPError as e:
            return Response(
                {'error': f'Analytics service error: {str(e)}'},
                status=e.response.status_code
            )
        except requests.RequestException as e:
            return Response(
                {'error': f'Error connecting to analytics service: {str(e)}'},
                status=status.HTTP_502_BAD_GATEWAY
            )
        except Exception as e:
            return Response(
                {'error': f'Unexpected error: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DashboardViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing dashboards
    """
    serializer_class = DashboardSerializer
    permission_classes = [IsAuthenticated, AnalyticsPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_public', 'is_active', 'owner']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['created_at', 'updated_at', 'name', 'code', 'sort_order']
    ordering = ['-updated_at']  # Default ordering by updated_at descending

    def get_queryset(self):
        user = self.request.user
        # Return dashboards owned by the user or public dashboards
        return Dashboard.objects.filter(owner=user) | Dashboard.objects.filter(is_public=True)

    @requires_permission(('analytics', 'create'))
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @requires_permission(('analytics', 'update'))
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @requires_permission(('analytics', 'delete'))
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    @requires_permission(('analytics', 'get'))
    @action(detail=True, methods=['get'])
    def all_charts(self, request, pk=None):
        """
        Get all charts for a dashboard, including those in chart groups
        """
        dashboard = self.get_object()
        charts = ChartService.get_charts_by_dashboard(dashboard.id)
        serializer = ChartSerializer(charts, many=True)
        return Response(serializer.data)


class ChartGroupViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing chart groups
    """
    serializer_class = ChartGroupSerializer
    permission_classes = [IsAuthenticated, AnalyticsPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['dashboard', 'is_active']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['created_at', 'updated_at', 'name', 'code', 'sort_order']
    ordering = ['-updated_at']  # Default ordering by updated_at descending

    def get_queryset(self):
        user = self.request.user
        # Return chart groups from dashboards owned by the user or public dashboards
        return (
            ChartGroup.objects.filter(dashboard__owner=user) |
            ChartGroup.objects.filter(dashboard__is_public=True)
        )

    @requires_permission(('analytics', 'create'))
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @requires_permission(('analytics', 'update'))
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @requires_permission(('analytics', 'delete'))
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    @requires_permission(('analytics', 'get'))
    @action(detail=True, methods=['get'])
    def charts(self, request, pk=None):
        """
        Get all charts for a specific chart group
        """
        chart_group = self.get_object()
        charts = ChartService.get_charts_by_chart_group(chart_group.id)
        serializer = ChartSerializer(charts, many=True)
        return Response(serializer.data)


class ChartViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing charts
    """
    serializer_class = ChartSerializer
    permission_classes = [IsAuthenticated, AnalyticsPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['dashboard', 'chart_group', 'chart_type', 'is_active']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['created_at', 'updated_at', 'name', 'code', 'sort_order']
    ordering = ['-updated_at']  # Default ordering by updated_at descending

    def get_queryset(self):
        user = self.request.user
        # Return charts from dashboards owned by the user or public dashboards
        return (
            Chart.objects.filter(dashboard__owner=user) |
            Chart.objects.filter(dashboard__is_public=True) |
            Chart.objects.filter(chart_group__dashboard__owner=user) |
            Chart.objects.filter(chart_group__dashboard__is_public=True)
        ).distinct()

    @action(detail=False, methods=['get'])
    def filter_schema(self, request):
        """
        Get filter schema documentation

        Returns documentation about filter types and their usage patterns.
        If chart_id is provided as a query parameter, also returns the specific
        filter configuration for that chart.
        """
        chart_id = request.query_params.get('chart_id')

        # Get filter schema documentation
        filter_schema = FilterSchemaService.get_chart_filter_schema(
            chart_id=int(chart_id) if chart_id and chart_id.isdigit() else None
        )

        # Use the serializer to validate and format the response
        serializer = FilterSchemaSerializer(filter_schema)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def filter_schema_for_chart(self, request, pk=None):
        """
        Get filter schema documentation for a specific chart
        """
        # Get filter schema documentation for the specified chart
        filter_schema = FilterSchemaService.get_chart_filter_schema(chart_id=pk)

        # Use the serializer to validate and format the response
        serializer = FilterSchemaSerializer(filter_schema)
        return Response(serializer.data)

    def get_queryset(self):
        user = self.request.user
        # Return charts from dashboards owned by the user or public dashboards
        # Also include charts that belong to chart groups in dashboards owned by the user or public dashboards
        return (
            Chart.objects.filter(dashboard__owner=user) |
            Chart.objects.filter(dashboard__is_public=True) |
            Chart.objects.filter(chart_group__dashboard__owner=user) |
            Chart.objects.filter(chart_group__dashboard__is_public=True)
        )

    @requires_permission(('analytics', 'create'))
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @requires_permission(('analytics', 'update'))
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @requires_permission(('analytics', 'delete'))
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    @requires_permission(('analytics', 'get'))
    @action(detail=True, methods=['get'])
    def data(self, request, pk=None):
        """
        Get the data for a chart with optional filters

        Query Parameters:
            Filters based on chart's filter_config field. Each filter parameter should match
            a key in the chart's filter_config.

            For 'date' and 'int' type filters:
                - Value should be an array with 1 or 2 elements
                - Single value for exact match, two values for range
                - Example: ?date_field=["2023-01-01","2023-12-31"]

            For 'bool' type filters:
                - Value should be 0 or 1
                - Example: ?active_flag=1

            For 'str' type filters:
                - Value is a string for exact match
                - Use % at start/end for LIKE operator
                - Example: ?name=John or ?name=%Joh% for LIKE
        """
        chart = self.get_object()

        try:
            # Get the original query
            query = chart.query

            # Use FilterService to handle filter processing
            filter_service = FilterService()
            filter_conditions = filter_service.build_filter_conditions(chart.filter_config, request.query_params)
            modified_query = filter_service.apply_filters_to_query(query, filter_conditions)

            # Execute the modified query
            clickhouse_service = ClickHouseService()
            print("__clickhouse_service__", clickhouse_service)
            print("__modified_query__", modified_query)
            df = clickhouse_service.execute_query(modified_query)
            print("__df__", df)

            # Transform the data to ECharts format
            chart_data = ChartService.transform_to_echarts(df, chart.chart_type, chart.config)
            print("__chart_data__", chart_data)

            return Response({
                "chart_data": chart_data,
                "chart_instance": ChartSerializer(chart).data
            })
        except AttributeError as e:
            # Handle specific attribute errors (like 'list' object has no attribute 'update')
            return Response(
                {'error': f'Chart configuration error: {str(e)}. Please check the chart configuration format.'},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            # Log the full error for debugging
            import traceback
            print(f"Error in chart data endpoint: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return Response(
                {'error': f'Error fetching chart data: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
