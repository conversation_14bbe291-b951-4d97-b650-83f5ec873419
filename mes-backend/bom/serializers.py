"""
Serializers for BOM models.

This module provides serializers for the BOM models, enabling API functionality
and data transformation for the BOM feature.
"""
from rest_framework import serializers
from catalog.models import Product
from catalog.serializers import ProductDetailSerializer
from .models import B<PERSON>Header, BOMItem


class BOMItemSerializer(serializers.ModelSerializer):
    """
    Serializer for BOM items.
    """
    component_detail = ProductDetailSerializer(source='component', read_only=True)
    
    class Meta:
        model = BOMItem
        fields = [
            'id', 'bom_header', 'component', 'component_detail', 
            'parent_item', 'quantity', 'position', 'item_type', 
            'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']


class BOMItemNestedSerializer(serializers.ModelSerializer):
    """
    Nested serializer for BOM items, used for hierarchical representation.
    """
    component_detail = serializers.SerializerMethodField()
    children = serializers.SerializerMethodField()
    
    class Meta:
        model = BOMItem
        fields = [
            'id', 'component', 'component_detail', 'quantity', 
            'position', 'item_type', 'notes', 'children'
        ]
    
    def get_component_detail(self, obj):
        """Get basic component details"""
        return {
            'id': obj.component.id,
            'code': obj.component.code,
            'name': obj.component.name,
            'description': obj.component.description
        }
    
    def get_children(self, obj):
        """Recursively get children of this item"""
        children = obj.children.all().select_related('component')
        return BOMItemNestedSerializer(children, many=True).data


class BOMHeaderSerializer(serializers.ModelSerializer):
    """
    Serializer for BOM headers.
    """
    product_detail = ProductDetailSerializer(source='product', read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = BOMHeader
        fields = [
            'id', 'name', 'code', 'description', 'is_active',
            'product', 'product_detail', 'status',
            'effective_date', 'created_by', 'created_by_username',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'created_by_username']


class BOMHeaderDetailSerializer(serializers.ModelSerializer):
    """
    Detailed serializer for BOM headers, including top-level items.
    """
    product_detail = serializers.SerializerMethodField()
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    top_level_items = serializers.SerializerMethodField()
    
    class Meta:
        model = BOMHeader
        fields = [
            'id', 'name', 'code', 'description', 'is_active',
            'product', 'product_detail', 'status',
            'effective_date', 'created_by', 'created_by_username',
            'created_at', 'updated_at', 'top_level_items'
        ]
        read_only_fields = ['created_at', 'updated_at', 'created_by_username', 'top_level_items']
    
    def get_product_detail(self, obj):
        """Get basic product details"""
        return {
            'id': obj.product.id,
            'code': obj.product.code,
            'name': obj.product.name,
            'description': obj.product.description
        }
    
    def get_top_level_items(self, obj):
        """Get all top-level items in this BOM"""
        top_items = obj.bom_items.filter(parent_item__isnull=True).select_related('component')
        return BOMItemNestedSerializer(top_items, many=True).data
