"""
Views for BOM API endpoints.

This module provides API views for the BOM feature, enabling CRUD operations
and specialized BOM queries.
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend, FilterSet
from django_filters import filters as django_filters
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from .models import BOMHeader, BOMItem
from .serializers import (
    BOMHeaderSerializer, BOMHeaderDetailSerializer,
    BOMItemSerializer, BOMItemNestedSerializer
)
from .services import BOMService


class BOMHeaderFilter(FilterSet):
    name = django_filters.CharFilter(lookup_expr='icontains')
    code = django_filters.CharFilter(lookup_expr='exact')

    class Meta:
        model = BOMHeader
        fields = ['name', 'code']


class BOMHeaderViewSet(viewsets.ModelViewSet):
    """
    API viewset for BOM headers.

    Provides CRUD operations for BOM headers and additional actions for
    retrieving the complete BOM structure.
    """
    queryset = BOMHeader.objects.all().select_related('product', 'created_by')
    serializer_class = BOMHeaderSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = BOMHeaderFilter
    ordering_fields = ['created_at', 'updated_at', 'name', 'code', 'status', 'effective_date']
    ordering = ['-updated_at']  # Default ordering by updated_at descending

    def get_serializer_class(self):
        """
        Return different serializers based on the action.
        """
        if self.action == 'retrieve':
            return BOMHeaderDetailSerializer
        return BOMHeaderSerializer

    def perform_create(self, serializer):
        """
        Set the created_by field to the current user when creating a BOM header.
        """
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=['get'])
    def complete_bom(self, request, pk=None):
        """
        Get the complete BOM structure for a specific BOM header.
        """
        bom_header = self.get_object()
        complete_bom = BOMService.get_complete_bom(bom_header.id)
        return Response(complete_bom)

    @action(detail=True, methods=['get'])
    def flat_bom(self, request, pk=None):
        """
        Get a flattened list of all items in the BOM.
        """
        bom_header = self.get_object()
        flat_bom = BOMService.get_flat_bom(bom_header.id)
        return Response(flat_bom)

    @action(detail=True, methods=['get'])
    def bom_levels(self, request, pk=None):
        """
        Get BOM items organized by their level in the hierarchy.
        """
        bom_header = self.get_object()
        levels = BOMService.get_bom_levels(bom_header.id)

        # Convert the dictionary to a list for easier consumption by clients
        result = []
        for level, items in levels.items():
            result.append({
                'level': level,
                'items': BOMItemSerializer(items, many=True).data
            })

        return Response(result)

    @action(detail=True, methods=['get'])
    def validate(self, request, pk=None):
        """
        Validate the BOM structure for circular references and other issues.
        """
        bom_header = self.get_object()
        validation_result = BOMService.validate_bom_structure(bom_header.id)
        return Response(validation_result)

    @swagger_auto_schema(
        method='get',
        operation_description="Get all BOMs for a specific product, including their complete structures.",
        manual_parameters=[
            openapi.Parameter(
                'product_id',
                openapi.IN_QUERY,
                description="The product ID (required if product_code not provided)",
                type=openapi.TYPE_INTEGER,
                required=False
            ),
            openapi.Parameter(
                'product_code',
                openapi.IN_QUERY,
                description="The product code (required if product_id not provided)",
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'status',
                openapi.IN_QUERY,
                description="Filter by BOM status (e.g., 'active', 'draft', 'obsolete')",
                type=openapi.TYPE_STRING,
                enum=['active', 'draft', 'obsolete'],
                required=False
            ),
        ],
        responses={
            200: "Returns product details and all associated BOMs with their structures",
            400: "Bad request - Missing required parameters",
            404: "Product not found"
        }
    )
    @action(detail=False, methods=['get'])
    def product_boms(self, request):
        """
        Get all BOMs for a specific product, including their complete structures.

        Query Parameters:
            product_id: The product ID (required if product_code not provided)
            product_code: The product code (required if product_id not provided)
            status: Optional filter for BOM status (e.g., 'active', 'draft', 'obsolete')

        Returns:
            A dictionary with product details and all associated BOMs with their structures
        """
        product_id = request.query_params.get('product_id')
        product_code = request.query_params.get('product_code')
        bom_status = request.query_params.get('status')

        # Check if either product_id or product_code is provided
        if not product_id and not product_code:
            return Response(
                {'error': 'Either product_id or product_code parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Use product_id if provided, otherwise use product_code
        product_identifier = product_id if product_id else product_code

        # Get BOMs for the product
        result = BOMService.get_product_boms(product_identifier, bom_status)

        # Check if there was an error
        if 'error' in result and result.get('product') is None:
            return Response(result, status=status.HTTP_404_NOT_FOUND)

        return Response(result)


class BOMItemViewSet(viewsets.ModelViewSet):
    """
    API viewset for BOM items.

    Provides CRUD operations for BOM items and additional actions for
    retrieving children of a specific item.
    """
    queryset = BOMItem.objects.all().select_related('bom_header', 'component', 'parent_item')
    serializer_class = BOMItemSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['bom_header', 'component', 'parent_item', 'item_type']
    search_fields = ['component__name', 'component__code', 'notes']
    ordering_fields = ['created_at', 'updated_at', 'quantity', 'position', 'item_type']
    ordering = ['-updated_at']  # Default ordering by updated_at descending

    @action(detail=True, methods=['get'])
    def children(self, request, pk=None):
        """
        Get all children of a specific BOM item.
        """
        item = self.get_object()
        children = BOMService.get_children(item.id)
        return Response(children)

    @action(detail=False, methods=['get'])
    def component_usage(self, request):
        """
        Find all BOMs where a specific component is used.
        """
        component_id = request.query_params.get('component_id')
        if not component_id:
            return Response(
                {'error': 'component_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        usage = BOMService.find_component_usage(component_id)
        return Response(usage)
