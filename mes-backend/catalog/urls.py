from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import ProductPartViewSet, ProductViewSet, ProductListViewSet, ProductTypeViewSet

router = DefaultRouter()
router.register(r'parts', ProductPartViewSet)
router.register(r'products', ProductViewSet)
router.register(r'products-list', ProductListViewSet)
router.register(r'product-types', ProductTypeViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
