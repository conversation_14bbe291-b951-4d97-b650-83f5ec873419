from django.utils import timezone
from datetime import timedelta
from rest_framework import serializers
from authentication.serializers.auth_serializers import UserSummarySerializer
from workflow_config.models import FormConfig
from workflow_config.serializers.form_serializers import FormConfigSummarySerializer
from ..models import ManufacturingEvent


class ManufacturingEventSummarySerializer(serializers.ModelSerializer):
    form_details = FormConfigSummarySerializer(
        source='form',
        read_only=True
    )
    created_by = UserSummarySerializer(
        read_only=True
    )

    class Meta:
        model = ManufacturingEvent
        exclude = ['product', 'scanner', 'form', 'work_order', 'board']
        read_only_fields = ['created_at']


class ValidationErrorSerializer(serializers.Serializer):
    field = serializers.CharField()
    error = serializers.CharField()
    value = serializers.JSONField(required=False, allow_null=True)  # Make value optional and allow null


class BulkManufacturingEventSerializer(serializers.ListSerializer):
    def create(self, validated_data):
        instances = []
        base_time = timezone.now()
        # Defining a time increment(one microsecond) for each event to ensure ascending order.
        increment = timedelta(microseconds=1)
        request = self.context.get('request', None)
        user = request.user if request and hasattr(request, 'user') else None

        for i, item in enumerate(validated_data):
            item['created_at'] = base_time + i * increment
            if user and not item.get('created_by'):
                item['created_by'] = user
            instances.append(ManufacturingEvent(**item))

        # Bulk create and return the created instances
        created_instances = ManufacturingEvent.objects.bulk_create(instances)
        return created_instances


class ManufacturingEventSerializer(serializers.ModelSerializer):
    """
    Serializer for ManufacturingEvent model
    """
    validation_errors = serializers.ListField(
        child=ValidationErrorSerializer(),
        required=False,
        allow_empty=True,
        allow_null=True,
        default=list
    )

    # For write operations (accepts form ID)
    form = serializers.PrimaryKeyRelatedField(
        queryset=FormConfig.objects.all(),
        write_only=True
    )

    # For read operations (returns form details)
    form_details = FormConfigSummarySerializer(
        source='form',
        read_only=True
    )

    # For read operations (returns user details)
    created_by = UserSummarySerializer(
        read_only=True
    )

    class Meta:
        model = ManufacturingEvent
        fields = '__all__'
        read_only_fields = ['created_by', 'created_at', 'form_details']
        list_serializer_class = BulkManufacturingEventSerializer
