from rest_framework import serializers
from django.contrib.auth import get_user_model

from .models import Shift, LineCapacity, ProductionSchedule, ProductionMetrics, DowntimeLog

User = get_user_model()


class ShiftSerializer(serializers.ModelSerializer):
    """Serializer for Shift model"""

    class Meta:
        model = Shift
        fields = [
            'id', 'name', 'code', 'description', 'start_time', 'end_time',
            'duration_hours', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate(self, data):
        """Custom validation for shift data"""
        # Create a temporary instance for validation
        instance = Shift(**data)
        instance.clean()  # This will run the model's clean method
        return data


class LineCapacitySerializer(serializers.ModelSerializer):
    """Serializer for LineCapacity model"""
    line_name = serializers.Char<PERSON>ield(source='line.name', read_only=True)
    product_name = serializers.Char<PERSON>ield(source='product.name', read_only=True)
    product_code = serializers.CharField(source='product.code', read_only=True)
    capacity_id = serializers.CharField(read_only=True)

    class Meta:
        model = LineCapacity
        fields = [
            'id', 'line', 'line_name', 'product', 'product_name', 'product_code',
            'capacity_id', 'uph', 'manpower', 'upph', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'capacity_id', 'created_at', 'updated_at']

    def validate(self, data):
        """Custom validation for line capacity data"""
        # Create a temporary instance for validation
        instance = LineCapacity(**data)
        instance.clean()  # This will run the model's clean method
        return data


class ProductionMetricsSerializer(serializers.ModelSerializer):
    """Serializer for ProductionMetrics model (read-only)"""
    schedule_info = serializers.SerializerMethodField()
    line_name = serializers.CharField(source='schedule.line.name', read_only=True)
    production_date = serializers.DateField(source='schedule.production_date', read_only=True)
    work_order_number = serializers.CharField(source='schedule.work_order.order_no', read_only=True)

    class Meta:
        model = ProductionMetrics
        fields = [
            'id', 'schedule', 'schedule_info', 'line_name', 'production_date',
            'work_order_number', 'line_capacity', 'machine_runtime_hours',
            'target_quantity', 'actual_ok_quantity', 'efficiency_percentage',
            'quality_percentage', 'upph_actual', 'line_capacity_calculated'
        ]
        read_only_fields = [
            'id', 'schedule', 'schedule_info', 'line_name', 'production_date',
            'work_order_number', 'line_capacity', 'machine_runtime_hours',
            'target_quantity', 'actual_ok_quantity', 'efficiency_percentage',
            'quality_percentage', 'upph_actual', 'line_capacity_calculated',
            'created_at', 'updated_at'
        ]  # All fields are read-only as metrics are auto-calculated

    def get_schedule_info(self, obj):
        """Get formatted schedule information"""
        schedule = obj.schedule
        return f"{schedule.line.name} - {schedule.production_date} {schedule.start_time}-{schedule.end_time}"


class ProductionScheduleSerializer(serializers.ModelSerializer):
    """Serializer for ProductionSchedule model"""
    line_name = serializers.CharField(source='line.name', read_only=True)
    work_order_no = serializers.CharField(source='work_order.order_no', read_only=True)
    shift_name = serializers.CharField(source='shift.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    metrics = ProductionMetricsSerializer(read_only=True)

    # Metrics information
    has_metrics = serializers.SerializerMethodField()

    class Meta:
        model = ProductionSchedule
        fields = [
            'id', 'production_date', 'start_time', 'end_time', 'line', 'line_name',
            'work_order', 'work_order_no', 'shift', 'shift_name',
            'planned_break_minutes', 'downtime_minutes', 'downtime_reason', 'manpower',
            'actual_quantity', 'rejection_quantity',
            'created_by', 'created_by_name', 'has_metrics', 'created_at', 'updated_at', 'metrics'
        ]
        read_only_fields = [
            'id', 'created_by', 'has_metrics', 'created_at', 'updated_at'
        ]

    def get_has_metrics(self, obj):
        """Check if production metrics exist for this schedule"""
        try:
            return hasattr(obj, 'metrics') and obj.metrics is not None
        except ProductionMetrics.DoesNotExist:
            return False

    def validate(self, data):
        """Custom validation for production schedule data"""
        # Create a temporary instance for validation
        instance = ProductionSchedule(**data)
        if hasattr(self, 'instance') and self.instance:
            # Update existing instance attributes with new data
            for attr, value in data.items():
                setattr(instance, attr, value)
        
        # Only run clean if all required fields are present
        if all(hasattr(instance, field) and getattr(instance, field) is not None 
               for field in ['shift', 'start_time', 'end_time']):
            instance.clean()
        return data

    def create(self, validated_data):
        """Create a new production schedule"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class DowntimeLogSerializer(serializers.ModelSerializer):
    """Serializer for DowntimeLog model"""
    schedule_info = serializers.SerializerMethodField()
    logged_by_name = serializers.CharField(source='logged_by.username', read_only=True)

    class Meta:
        model = DowntimeLog
        fields = [
            'id', 'schedule', 'schedule_info', 'downtime_type', 'duration_minutes',
            'reason', 'description', 'start_time', 'end_time', 'logged_by',
            'logged_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_schedule_info(self, obj):
        """Get formatted schedule information"""
        schedule = obj.schedule
        return f"{schedule.line.name} - {schedule.production_date} {schedule.start_time}-{schedule.end_time}"

    def validate(self, data):
        """Custom validation for downtime log data"""
        # Create a temporary instance for validation
        instance = DowntimeLog(**data)
        instance.clean()  # This will run the model's clean method
        return data

    def create(self, validated_data):
        """Create a new downtime log"""
        # Set the logged_by field to the current user
        validated_data['logged_by'] = self.context['request'].user
        return super().create(validated_data)
