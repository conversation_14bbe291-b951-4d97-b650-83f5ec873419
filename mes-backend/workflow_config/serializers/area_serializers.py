from rest_framework import serializers
from ..models import Area, Factory


class AreaSerializer(serializers.ModelSerializer):
    """
    Serializer for the Area model.
    
    Provides CRUD operations for Area instances with proper field validation
    and nested serializations for related objects.
    """
    factory_name = serializers.ReadOnlyField(source='factory.name')
    factory_location = serializers.ReadOnlyField(source='factory.location')
    created_by_username = serializers.ReadOnlyField(source='created_by.username')
    atype_display = serializers.ReadOnlyField(source='get_atype_display')
    
    # Write-only field for factory assignment
    factory = serializers.PrimaryKeyRelatedField(
        queryset=Factory.objects.filter(is_active=True),
        write_only=True
    )
    
    # Read-only nested factory details
    factory_details = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Area
        fields = [
            'id', 'name', 'code', 'description', 'is_active',
            'factory', 'factory_details', 'factory_name', 'factory_location',
            'atype', 'atype_display',
            'created_by', 'created_by_username',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'created_by', 'created_by_username', 
            'created_at', 'updated_at'
        ]

    def get_factory_details(self, obj):
        """Get detailed factory information"""
        if obj.factory:
            return {
                'id': obj.factory.id,
                'name': obj.factory.name,
                'code': obj.factory.code,
                'location': obj.factory.location,
                'is_active': obj.factory.is_active
            }
        return None

    def validate_code(self, value):
        """
        Validate that the area code is unique.
        """
        # Check if this is an update operation
        if self.instance and self.instance.code == value:
            return value
            
        if Area.objects.filter(code=value).exists():
            raise serializers.ValidationError(
                "An area with this code already exists."
            )
        return value

    def validate_name(self, value):
        """
        Validate area name.
        """
        if not value or not value.strip():
            raise serializers.ValidationError(
                "Area name cannot be empty."
            )
        return value.strip()

    def validate_atype(self, value):
        """
        Validate area type.
        """
        valid_types = [choice[0] for choice in Area.AREATYPECHOICES]
        if value not in valid_types:
            raise serializers.ValidationError(
                f"Invalid area type. Must be one of: {', '.join(valid_types)}"
            )
        return value

    def validate(self, attrs):
        """
        Perform object-level validation.
        """
        # Ensure factory is active
        factory = attrs.get('factory')
        if factory and not factory.is_active:
            raise serializers.ValidationError({
                'factory': 'Cannot assign area to an inactive factory.'
            })
        
        return attrs


class AreaSummarySerializer(serializers.ModelSerializer):
    """
    Lightweight serializer for Area model used in nested relationships.
    """
    factory_name = serializers.ReadOnlyField(source='factory.name')
    atype_display = serializers.ReadOnlyField(source='get_atype_display')

    class Meta:
        model = Area
        fields = [
            'id', 'name', 'code', 'description', 'is_active',
            'factory_name', 'atype', 'atype_display'
        ]
