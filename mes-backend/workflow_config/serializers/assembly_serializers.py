from rest_framework import serializers
from ..models import AssemblyLine


class AssemblyLineSerializer(serializers.ModelSerializer):
    area_name = serializers.CharField(source='area.name', read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)

    class Meta:
        model = AssemblyLine
        fields = ['id', 'name', 'code', 'description', 'is_active', 'area', 'area_name', 'created_by', 'created_by_username', 'created_at', 'updated_at']
        read_only_fields = ['created_by', 'created_by_username', 'created_at', 'updated_at', 'area_name']
