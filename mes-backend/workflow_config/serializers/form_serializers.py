from rest_framework import serializers
from ..models import FormConfig


class FormConfigSummarySerializer(serializers.ModelSerializer):
    """Serializer for minimal form config details in event responses"""
    class Meta:
        model = FormConfig
        fields = ['id', 'name', 'code', 'description', 'is_active']


class BaseFormConfigSerializer(serializers.ModelSerializer):
    # Read-only fields for process block details
    process_block_details = serializers.SerializerMethodField(read_only=True)
    process_block_name = serializers.ReadOnlyField(source='process_block.name')
    created_by_username = serializers.ReadOnlyField(source='created_by.username')

    class Meta:
        model = FormConfig
        fields = '__all__'
        read_only_fields = ['id', 'created_by', 'created_at', 'updated_at', 'version']

    def get_process_block_details(self, obj):
        """Get detailed process block information"""
        if obj.process_block:
            return {
                'id': obj.process_block.id,
                'name': obj.process_block.name,
                'code': obj.process_block.code,
                'description': obj.process_block.description,
                'is_active': obj.process_block.is_active,
                'area_name': obj.process_block.area.name if obj.process_block.area else None,
                'line_name': obj.process_block.line_loc.name if obj.process_block.line_loc else None
            }
        return None

    # def validate_form_schema(self, value):
    #     required_keys = {'fields'}
    #     if not all(key in value for key in required_keys):
    #         raise serializers.ValidationError(
    #             f"Form schema must contain all required keys: {required_keys}"
    #         )

    #     for field in value.get('fields', []):
    #         if not all(key in field for key in {'name', 'label', 'type', 'id',}):
    #             raise serializers.ValidationError(
    #                 "Each form field must contain 'name', 'label', 'type', and 'id'"
    #             )
    #     return value


class FormConfigSerializer(BaseFormConfigSerializer):
    class Meta(BaseFormConfigSerializer.Meta):
        fields = [
            'id', 'name', 'code', 'description', 'is_active',
            'process_block', 'process_block_details', 'process_block_name',
            'form_schema', 'version',
            'created_by', 'created_by_username',
            'created_at', 'updated_at'
        ]


class FormConfigUpdateSerializer(BaseFormConfigSerializer):
    class Meta(BaseFormConfigSerializer.Meta):
        fields = [
            'id', 'name', 'code', 'description', 'is_active',
            'process_block', 'process_block_details', 'process_block_name',
            'form_schema', 'version',
            'created_by', 'created_by_username',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_by', 'created_at', 'updated_at', 'version', 'code']
