from rest_framework import serializers
from ..models import ReferenceCategory, ReferenceValue


class ReferenceValueSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)

    class Meta:
        model = ReferenceValue
        fields = ['id', 'code', 'value', 'description', 'sort_order', 'is_active', 'category', 'category_name', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at', 'category_name']


class ReferenceCategorySerializer(serializers.ModelSerializer):
    values = ReferenceValueSerializer(many=True, read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)

    class Meta:
        model = ReferenceCategory
        fields = ['id', 'code', 'name', 'description', 'is_active', 'values', 'created_by', 'created_by_username', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at', 'created_by', 'created_by_username']
