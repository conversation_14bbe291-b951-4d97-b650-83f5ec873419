from datetime import datetime
from rest_framework import serializers, status
from workflow_config.models import Routing, RoutingProduct, ProcessBlock
from catalog.serializers import ProductSummarySerializer
from authentication.serializers.auth_serializers import UserSummarySerializer
from catalog.models import Product, Scanner
from workflow_config.services.execution_validation_service import ExecutionValidationService


class RoutingSummarySerializer(serializers.ModelSerializer):
    """
    Serializer for summarized Routing information
    """
    products_details = ProductSummarySerializer(source='products', many=True, read_only=True)
    created_by = UserSummarySerializer(read_only=True)

    class Meta:
        model = Routing
        fields = ['id', 'name', 'code', 'description', 'products_details', 'created_by', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']


class RoutingDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for detailed Routing information including schema
    """
    products_details = ProductSummarySerializer(source='products', many=True, read_only=True)
    created_by = UserSummarySerializer(read_only=True)
    product_ids = serializers.PrimaryKeyRelatedField(
        source='products',
        queryset=Product.objects.all(),
        many=True,
        write_only=True,
        required=False
    )

    class Meta:
        model = Routing
        fields = ['id', 'name', 'code', 'schema', 'products_details', 'product_ids', 'created_by', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']

    def create(self, validated_data):
        products = validated_data.pop('products', [])
        routing = Routing.objects.create(**validated_data)

        # Add products to the routing, validating that each product can only have one routing
        for product in products:
            # Check if product already has a routing
            if RoutingProduct.objects.filter(product=product).exists():
                raise serializers.ValidationError(
                    f"Product '{product.name}' is already associated with another routing. "
                    f"A product can only have one routing at this time."
                )

            RoutingProduct.objects.create(routing=routing, product=product)

        return routing

    def update(self, instance, validated_data):
        products = validated_data.pop('products', None)

        # Update routing fields
        instance.name = validated_data.get('name', instance.name)
        instance.code = validated_data.get('code', instance.code)
        instance.schema = validated_data.get('schema', instance.schema)
        instance.save()

        # Update products if provided
        if products is not None:
            # Get current products to avoid unnecessary validation
            current_product_ids = set(instance.products.values_list('id', flat=True))
            new_product_ids = set(product.id for product in products)

            # Products to add (not currently associated with this routing)
            products_to_add = [p for p in products if p.id not in current_product_ids]

            # Validate that new products don't already have a routing
            for product in products_to_add:
                if RoutingProduct.objects.filter(product=product).exclude(routing=instance).exists():
                    raise serializers.ValidationError(
                        f"Product '{product.name}' is already associated with another routing. "
                        f"A product can only have one routing at this time."
                    )

            # Clear existing products that are not in the new list
            RoutingProduct.objects.filter(
                routing=instance,
                product_id__in=current_product_ids - new_product_ids
            ).delete()

            # Add new products
            for product in products_to_add:
                RoutingProduct.objects.create(routing=instance, product=product)

        return instance

    def to_representation(self, instance):
        """
        Override to_representation to inject ProcessBlock names into the schema
        """
        representation = super().to_representation(instance)

        # Get the schema from the representation
        schema = representation.get('schema', {})
        routing_schema = schema.get('routing_schema', {})
        components = routing_schema.get('components', {})

        if components:
            component_codes = list(components.keys())
            process_blocks = {pb.code: pb.name for pb in ProcessBlock.objects.filter(code__in=component_codes)}
            scanners = {s.code: s.name for s in Scanner.objects.filter(code__in=component_codes)}
            all_names = {**process_blocks, **scanners}

            # Inject names into components
            for code, component in components.items():
                if code in all_names:
                    component['name'] = all_names[code]

            representation['schema']['routing_schema']['components'] = components

        return representation


class ExecutedRoutingSerializer(serializers.ModelSerializer):
    route_sequence = serializers.SerializerMethodField()

    class Meta:
        model = Routing
        fields = ['id', 'name', 'code', 'schema', 'route_sequence', 'created_at']

    def get_route_sequence(self, obj):
        execution = self.context.get('execution')
        schema = obj.schema.get('routing_schema', {})
        components = schema.get('components', {})
        connections = schema.get('route', {}).get('connections', {})

        # Start with the executed sequence
        route = execution.executed_sequence.copy() if execution.executed_sequence else []

        # Format time and ensure time field exists in all executed nodes
        for step in route:
            if 'time' not in step:
                step['time'] = None
            elif step['time'] is not None:
                try:
                    # Parse the ISO format time and convert to desired format
                    if isinstance(step['time'], str):
                        dt = datetime.fromisoformat(step['time'].replace('Z', '+00:00'))
                        step['time'] = dt.strftime('%Y-%m-%d %H:%M:%S')
                except (ValueError, TypeError):
                    # If parsing fails, keep the original value
                    pass

            # Add validity information for executed steps
            if 'event_id' in step:
                # Get the form code for this step if available
                process_block_code = step.get('executed')
                form_code = None
                if process_block_code and process_block_code in components:
                    form_code = components[process_block_code].get('form')

                # Get the appropriate validator for this form
                validator = ExecutionValidationService.get_validator_for_form(form_code=form_code)
                validity = validator(step['event_id'])
                step['validity'] = validity
            else:
                step['validity'] = None

        # Add the next executable node if it exists
        next_executable = execution.next_executable
        if next_executable:
            route.append({
                "executed": None,
                "should_execute": next_executable,
                "time": None,
                "validity": None  # Future nodes have no validity yet
            })

            # Find remaining nodes in the main path after next_executable
            remaining_nodes = self._get_remaining_nodes(next_executable, connections, components)

            # Add remaining nodes to the route
            for node in remaining_nodes:
                route.append({
                    "executed": None,
                    "should_execute": node,
                    "time": None,
                    "validity": None  # Future nodes have no validity yet
                })

        return route

    def _get_remaining_nodes(self, start_node, connections, components):
        """
        Traverse the routing schema to find the remaining nodes in the main path
        starting from the given node.

        Args:
            start_node (str): The node to start traversal from
            connections (dict): The connections object from the routing schema
            components (dict): The components object from the routing schema

        Returns:
            list: A list of node codes in the main path
        """
        remaining_nodes = []
        current_node = start_node

        # Traverse until we reach the end or a cycle
        visited = set([current_node])

        while True:
            # Get the current connection
            connection = connections.get(current_node, {})

            # Get the next node from towards.default
            towards = connection.get('towards', {})
            next_node = towards.get('default')

            # Check if we've reached the end
            if not next_node or next_node == 'end' or towards.get('end', False):
                break

            # Check if the next node is event_required
            component = components.get(next_node, {})
            event_required = component.get('event_required', True)

            # Only include nodes with event_required=true
            if event_required:
                remaining_nodes.append(next_node)

            # Move to the next node
            current_node = next_node

            # Check for cycles
            if current_node in visited:
                break
            visited.add(current_node)

        return remaining_nodes

    def to_representation(self, instance):
        data = super().to_representation(instance)

        # Get the schema from the representation
        schema = data.get('schema', {})
        routing_schema = schema.get('routing_schema', {})
        components = routing_schema.get('components', {})

        if components:
            # Get all component codes
            component_codes = list(components.keys())

            # Fetch ProcessBlock names for these codes
            process_blocks = {pb.code: pb.name for pb in ProcessBlock.objects.filter(code__in=component_codes)}

            # Fetch Scanner names for these codes (if any)
            scanners = {s.code: s.name for s in Scanner.objects.filter(code__in=component_codes)}

            # Combine both dictionaries
            all_names = {**process_blocks, **scanners}

            # Inject names into components
            for code, component in components.items():
                if code in all_names:
                    component['name'] = all_names[code]

            # Update the representation
            data['schema']['routing_schema']['components'] = components
            del data['schema']['routing_schema']['route'] # removing the `route` from the schema since `route_sequence` already provides enough information

        return data
