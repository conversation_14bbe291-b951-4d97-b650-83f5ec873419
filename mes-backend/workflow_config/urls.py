from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views.form_views import FormConfigViewSet
from .views.reference_views import ReferenceCategoryViewSet, ReferenceValueViewSet
from .views.aoi_views import ExcelUploadView
from .views.routing_views import RoutingViewSet, RoutingProductView, RoutingSerialView
from .views.process_views import ProcessBlockViewSet
from .views.process_form_views import ProcessBlockFormView
from .views.assembly_views import AssemblyLineViewSet
from .views.area_views import AreaViewSet

# Create a router and register our viewsets
router = DefaultRouter()
router.register(r'forms', FormConfigViewSet)
router.register(r'reference-categories', ReferenceCategoryViewSet)
router.register(r'reference-values', ReferenceValueViewSet)
router.register(r'routings', RoutingViewSet, basename='routing')
router.register(r'process-blocks', ProcessBlockViewSet)
router.register(r'assembly-lines', AssemblyLineViewSet)
router.register(r'areas', AreaViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('import-aoi-excel/', ExcelUploadView.as_view(), name='import-aoi-excel'),
    path(r'routings/by-product/<str:product_code>/', RoutingProductView.as_view(), name='product-routing'),
    path(r'routings/by-serial/<str:serial_number>/', RoutingSerialView.as_view(), name='serial-routing'),
    path(r'process-blocks-forms/', ProcessBlockFormView.as_view(), name='process-blocks-forms'),
]
