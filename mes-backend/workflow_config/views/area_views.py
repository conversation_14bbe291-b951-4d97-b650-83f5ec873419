from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from core.pagination import CustomPageNumberPagination
from authentication.decorators import requires_permission

from ..models import Area
from ..serializers.area_serializers import AreaSerializer


class AreaViewSet(viewsets.ModelViewSet):
    """
    API endpoint that allows Areas to be viewed or edited.
    
    list:
    Return a list of all Areas with optional filtering by factory, area type, and active status.
    
    create:
    Create a new Area instance.
    
    retrieve:
    Return the given Area.
    
    update:
    Update the given Area.
    
    partial_update:
    Partially update the given Area.
    
    destroy:
    Delete the given Area.
    """
    queryset = Area.objects.select_related(
        'factory', 'created_by'
    ).order_by('name')
    serializer_class = AreaSerializer
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = CustomPageNumberPagination
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    
    # Filter fields
    filterset_fields = {
        'factory': ['exact'],
        'atype': ['exact'],
        'is_active': ['exact'],
        'created_by': ['exact'],
    }
    
    # Search fields
    search_fields = ['name', 'code', 'description', 'factory__name', 'factory__location']
    
    # Ordering fields
    ordering_fields = ['name', 'code', 'created_at', 'updated_at', 'factory__name']
    ordering = ['name']

    @requires_permission(('area', 'get'))
    def list(self, request, *args, **kwargs):
        """
        List all areas with optional filtering.
        
        Query Parameters:
        - factory: Filter by factory ID
        - atype: Filter by area type ('sf' for Shop Floor, 'wh' for Warehouse)
        - is_active: Filter by active status (true/false)
        - created_by: Filter by creator user ID
        - search: Search in name, code, description, factory name, and factory location
        - ordering: Order by name, code, created_at, updated_at, or factory__name
        """
        queryset = self.filter_queryset(self.get_queryset())
        
        # Additional custom filtering
        factory_name = request.query_params.get('factory_name')
        if factory_name:
            queryset = queryset.filter(factory__name__icontains=factory_name)
        
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @requires_permission(('area', 'create'))
    def create(self, request, *args, **kwargs):
        """
        Create a new area.
        
        Required fields:
        - name: Area name
        - code: Unique area code
        - factory: Factory ID
        - atype: Area type ('sf' or 'wh')
        
        Optional fields:
        - description: Area description
        - is_active: Active status (defaults to True)
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, 
            status=status.HTTP_201_CREATED, 
            headers=headers
        )

    @requires_permission(('area', 'get'))
    def retrieve(self, request, *args, **kwargs):
        """
        Retrieve a specific area by ID.
        """
        return super().retrieve(request, *args, **kwargs)

    @requires_permission(('area', 'update'))
    def update(self, request, *args, **kwargs):
        """
        Update an area.
        """
        return super().update(request, *args, **kwargs)

    @requires_permission(('area', 'update'))
    def partial_update(self, request, *args, **kwargs):
        """
        Partially update an area.
        """
        return super().partial_update(request, *args, **kwargs)

    @requires_permission(('area', 'delete'))
    def destroy(self, request, *args, **kwargs):
        """
        Delete an area.
        
        Note: This will fail if the area has related assembly lines or process blocks.
        """
        instance = self.get_object()
        
        # Check for related objects that would prevent deletion
        if instance.lines.exists():
            return Response(
                {
                    'error': 'Cannot delete area with existing assembly lines. '
                             'Please remove or reassign assembly lines first.'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if instance.process_blocks.exists():
            return Response(
                {
                    'error': 'Cannot delete area with existing process blocks. '
                             'Please remove or reassign process blocks first.'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return super().destroy(request, *args, **kwargs)

    def perform_create(self, serializer):
        """
        Set the created_by field to the current user when creating an area.
        """
        serializer.save(created_by=self.request.user)

    def perform_update(self, serializer):
        """
        Perform the update operation.
        """
        serializer.save()
