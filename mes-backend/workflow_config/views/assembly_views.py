from rest_framework import viewsets, permissions
from rest_framework.filters import <PERSON><PERSON><PERSON><PERSON>, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from ..models import AssemblyLine
from ..serializers.assembly_serializers import AssemblyLineSerializer


class AssemblyLineViewSet(viewsets.ModelViewSet):
    queryset = AssemblyLine.objects.select_related('area', 'created_by').all()
    serializer_class = AssemblyLineSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['area', 'is_active']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['created_at', 'updated_at', 'name', 'code']
    ordering = ['-updated_at']  # Default ordering by updated_at descending
    filterset_fields = ['area']
    search_fields = ['name', 'code']
    ordering_fields = ['name', 'code']
    ordering = ['name']

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
