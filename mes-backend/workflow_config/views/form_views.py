from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from django.core.exceptions import ValidationError as DjValidationError
from rest_framework.permissions import IsAuthenticated
from catalog.models import ProductPart
from operation.serializers.event_serializers import ManufacturingEventSerializer
from authentication.decorators import requires_permission
from authentication.services.permission_service import PermissionService
from ..serializers.form_serializers import FormConfigSerializer, FormConfigUpdateSerializer
from ..services.form_service import FormService
from operation.services.serial_number_service import parse_serial_number
from ..models import FormConfig


from core.pagination import CustomPageNumberPagination

class FormConfigViewSet(viewsets.ModelViewSet):
    queryset = FormConfig.objects.select_related(
        'process_block',
        'process_block__area',
        'process_block__line_loc',
        'created_by'
    ).filter(is_active=True).order_by('id')
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination

    def get_serializer_class(self):
        if self.action in ['update', 'partial_update']:
            return FormConfigUpdateSerializer
        return FormConfigSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @requires_permission(('form', 'get'))
    def list(self, request, *args, **kwargs):
        """
        List all form configs that user has permission to view.
        For object-level permissions, only returns forms user can access.

        Query Parameters:
            name: Search by form name (case-insensitive partial match)
            code: Filter by form code (exact match)
        """
        queryset = self.get_queryset()

        # Apply filters
        name_search = request.query_params.get('name')
        code_filter = request.query_params.get('code')

        if name_search:
            queryset = queryset.filter(name__icontains=name_search)
        if code_filter:
            queryset = queryset.filter(code=code_filter)

        permissions = PermissionService._get_user_permissions(request.user.id)
        form_permissions = permissions.get('form', {})

        # If module requires object-level permissions, filter queryset
        if form_permissions.get('module_type') == 'object':
            # Check if user has all_objects_allowed for 'get' scope
            if form_permissions.get('all_objects_allowed', {}).get('get'):
                # User has access to all objects, no filtering needed
                pass
            else:
                # Get specific allowed objects
                allowed_objects = form_permissions.get('object_permissions', {}).get('get', set())
                if allowed_objects:
                    queryset = queryset.filter(id__in=allowed_objects)
                else:
                    queryset = queryset.none()  # No objects allowed

        # Apply any additional filtering
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @requires_permission(('form', 'create'))
    def create(self, request, *args, **kwargs):
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            headers = self.get_success_headers(serializer.data)
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED,
                headers=headers
            )
        except ValidationError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except DjValidationError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @requires_permission(
        ('form', 'get', lambda req: int(req.parser_context['kwargs'].get('pk')))
    )
    def retrieve(self, request, *args, **kwargs):
        """
        Get a specific form config by ID.
        Requires object-level 'get' permission for the requested form.
        """
        return super().retrieve(request, *args, **kwargs)
